import * as assert from 'assert';
import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import { CacheService } from '../services/cache';
import { CacheOptions } from '../interfaces/types';

/**
 * Test suite for the cache service
 */
suite('Cache Service Tests', () => {
  let cacheService: CacheService;
  let mockContext: vscode.ExtensionContext;
  let testCacheDir: string;

  setup(() => {
    // Create a temporary directory for testing
    testCacheDir = path.join(__dirname, 'test-cache');
    if (!fs.existsSync(testCacheDir)) {
      fs.mkdirSync(testCacheDir, { recursive: true });
    }

    // Mock extension context
    mockContext = {
      extensionPath: testCacheDir,
    } as vscode.ExtensionContext;

    // Mock getExtensionContext function
    // const originalGetExtensionContext = require('../extension').getExtensionContext; // Removed unused variable
    require('../extension').getExtensionContext = () => mockContext;

    cacheService = new CacheService();
  });

  teardown(async () => {
    // Clean up test cache
    try {
      await cacheService.clear();
      if (fs.existsSync(testCacheDir)) {
        fs.rmSync(testCacheDir, { recursive: true, force: true });
      }
    } catch (error) {
      console.warn('Cleanup error:', error);
    }
  });

  test('should cache and retrieve data', async () => {
    const testData = { message: 'Hello, World!', timestamp: Date.now() };
    const cacheKey = 'test-key';
    const options: CacheOptions = { ttl: 60000 }; // 1 minute

    // Cache the data
    await cacheService.set(cacheKey, testData, options);

    // Retrieve the data
    const retrievedData = await cacheService.get(cacheKey);

    assert.deepStrictEqual(retrievedData, testData);
  });

  test('should return null for non-existent cache key', async () => {
    const result = await cacheService.get('non-existent-key');
    assert.strictEqual(result, null);
  });

  test('should return null for expired cache entry', async () => {
    const testData = { message: 'This will expire' };
    const cacheKey = 'expiring-key';
    const options: CacheOptions = { ttl: 1 }; // 1ms TTL

    // Cache the data
    await cacheService.set(cacheKey, testData, options);

    // Wait for expiration
    await new Promise((resolve) => setTimeout(resolve, 10));

    // Try to retrieve expired data
    const result = await cacheService.get(cacheKey);
    assert.strictEqual(result, null);
  });

  test('should check if cache entry exists', async () => {
    const testData = { test: true };
    const cacheKey = 'exists-test';
    const options: CacheOptions = { ttl: 60000 };

    // Initially should not exist
    const existsBefore = await cacheService.has(cacheKey);
    assert.strictEqual(existsBefore, false);

    // Cache the data
    await cacheService.set(cacheKey, testData, options);

    // Now should exist
    const existsAfter = await cacheService.has(cacheKey);
    assert.strictEqual(existsAfter, true);
  });

  test('should delete cache entry', async () => {
    const testData = { toDelete: true };
    const cacheKey = 'delete-test';
    const options: CacheOptions = { ttl: 60000 };

    // Cache the data
    await cacheService.set(cacheKey, testData, options);

    // Verify it exists
    const existsBefore = await cacheService.has(cacheKey);
    assert.strictEqual(existsBefore, true);

    // Delete the entry
    await cacheService.delete(cacheKey);

    // Verify it's gone
    const existsAfter = await cacheService.has(cacheKey);
    assert.strictEqual(existsAfter, false);
  });

  test('should clear all cache entries', async () => {
    const options: CacheOptions = { ttl: 60000 };

    // Cache multiple entries
    await cacheService.set('key1', { data: 1 }, options);
    await cacheService.set('key2', { data: 2 }, options);
    await cacheService.set('key3', { data: 3 }, options);

    // Verify they exist
    assert.strictEqual(await cacheService.has('key1'), true);
    assert.strictEqual(await cacheService.has('key2'), true);
    assert.strictEqual(await cacheService.has('key3'), true);

    // Clear all
    await cacheService.clear();

    // Verify they're gone
    assert.strictEqual(await cacheService.has('key1'), false);
    assert.strictEqual(await cacheService.has('key2'), false);
    assert.strictEqual(await cacheService.has('key3'), false);
  });

  test('should generate consistent cache keys', () => {
    const endpoint = 'test-endpoint';
    const params = { userId: '123', teamId: 456 };
    const userId = '123';

    const key1 = cacheService.generateKey(endpoint, params, userId);
    const key2 = cacheService.generateKey(endpoint, params, userId);

    assert.strictEqual(key1, key2);
  });

  test('should generate different keys for different parameters', () => {
    const endpoint = 'test-endpoint';
    const params1 = { userId: '123', teamId: 456 };
    const params2 = { userId: '123', teamId: 789 };
    const userId = '123';

    const key1 = cacheService.generateKey(endpoint, params1, userId);
    const key2 = cacheService.generateKey(endpoint, params2, userId);

    assert.notStrictEqual(key1, key2);
  });

  test('should cleanup expired entries', async () => {
    const validOptions: CacheOptions = { ttl: 60000 }; // 1 minute
    const expiredOptions: CacheOptions = { ttl: 1 }; // 1ms

    // Cache some data with different TTLs
    await cacheService.set('valid-key', { valid: true }, validOptions);
    await cacheService.set('expired-key', { expired: true }, expiredOptions);

    // Wait for expiration
    await new Promise((resolve) => setTimeout(resolve, 10));

    // Run cleanup
    await cacheService.cleanup();

    // Valid entry should still exist
    assert.strictEqual(await cacheService.has('valid-key'), true);

    // Expired entry should be gone
    assert.strictEqual(await cacheService.has('expired-key'), false);
  });
});
