/**
 * Tests for debounce utilities
 */

import * as assert from 'assert';
import { debounce, createDebouncedFunction, cancelAllDebouncedOperations } from '../utils/debounce';

describe('Debounce Utilities', () => {
  afterEach(() => {
    // Clean up any pending operations after each test
    cancelAllDebouncedOperations();
  });

  describe('debounce', () => {
    it('should debounce function calls', (done) => {
      let callCount = 0;
      const testFunction = async (value: string) => {
        callCount++;
        return value;
      };

      const debouncedFunction = debounce(testFunction, 100);

      // Call the function multiple times rapidly
      debouncedFunction('test1');
      debouncedFunction('test2');
      debouncedFunction('test3');

      // Should only be called once after the delay
      setTimeout(() => {
        assert.strictEqual(
          callCount,
          1,
          'Function should only be called once after debounce delay',
        );
        done();
      }, 150);
    });

    it('should call function with latest arguments', (done) => {
      let lastValue: string | undefined;
      const testFunction = async (value: string) => {
        lastValue = value;
      };

      const debouncedFunction = debounce(testFunction, 50);

      debouncedFunction('first');
      debouncedFunction('second');
      debouncedFunction('third');

      setTimeout(() => {
        assert.strictEqual(
          lastValue,
          'third',
          'Function should be called with the latest arguments',
        );
        done();
      }, 100);
    });

    it('should handle errors gracefully', (done) => {
      let callCount = 0;
      const errorFunction = async () => {
        callCount++;
        throw new Error('Test error');
      };

      const debouncedFunction = debounce(errorFunction, 50);

      debouncedFunction();

      setTimeout(() => {
        assert.strictEqual(
          callCount,
          1,
          'Function should still be called even if it throws an error',
        );
        done();
      }, 100);
    });

    it('should reset timeout on subsequent calls', (done) => {
      let callCount = 0;
      const testFunction = async () => {
        callCount++;
      };

      const debouncedFunction = debounce(testFunction, 100);

      debouncedFunction();

      // Call again after 50ms (before first timeout completes)
      setTimeout(() => {
        debouncedFunction();
      }, 50);

      // Check after 120ms (70ms after second call)
      setTimeout(() => {
        assert.strictEqual(callCount, 0, 'Function should not have been called yet');
      }, 120);

      // Check after 200ms (100ms after second call)
      setTimeout(() => {
        assert.strictEqual(callCount, 1, 'Function should be called once after final timeout');
        done();
      }, 200);
    });
  });

  describe('createDebouncedFunction', () => {
    it('should create a debounced function with custom delay', (done) => {
      let callCount = 0;
      const testFunction = async () => {
        callCount++;
      };

      const debouncedFunction = createDebouncedFunction(testFunction, 75);

      debouncedFunction();
      debouncedFunction();

      setTimeout(() => {
        assert.strictEqual(callCount, 1, 'Custom debounced function should work correctly');
        done();
      }, 125);
    });
  });

  describe('cancelAllDebouncedOperations', () => {
    it('should cancel pending operations', (done) => {
      let callCount = 0;
      const testFunction = async () => {
        callCount++;
      };

      const debouncedFunction = debounce(testFunction, 100);

      debouncedFunction();

      // Cancel all operations before they execute
      setTimeout(() => {
        cancelAllDebouncedOperations();
      }, 50);

      // Check that function was not called
      setTimeout(() => {
        assert.strictEqual(callCount, 0, 'Function should not be called after cancellation');
        done();
      }, 150);
    });
  });
});
