/**
 * Debounce Utilities
 *
 * Provides debouncing functionality to prevent excessive API calls during rapid
 * configuration changes, status updates, and other frequent operations.
 */

import { updateStats } from './updateStats';
import { TIME } from '../constants';

/**
 * Generic debounce function for async functions
 * @param func The async function to debounce
 * @param delay The debounce delay in milliseconds
 * @returns A debounced version of the function
 */
export function debounce<T extends (...args: any[]) => Promise<any>>(
  func: T,
  delay: number,
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout | null = null;

  return (...args: Parameters<T>) => {
    // Clear the previous timeout if it exists
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    // Set a new timeout
    timeoutId = setTimeout(async () => {
      try {
        await func(...args);
      } catch (error) {
        // Error handling is done within the original function
        // We just ensure the debounce mechanism continues to work
      } finally {
        timeoutId = null;
      }
    }, delay);
  };
}

/**
 * Debounced version of updateStats for configuration changes
 * Prevents excessive API calls when multiple configuration settings are changed rapidly
 */
export const debouncedUpdateStatsForConfig = debounce(
  updateStats,
  TIME.DEBOUNCE_DELAYS.CONFIGURATION_CHANGE,
);

/**
 * Debounced version of updateStats for status updates
 * Prevents excessive API calls during rapid status update requests
 */
export const debouncedUpdateStatsForStatus = debounce(
  updateStats,
  TIME.DEBOUNCE_DELAYS.STATUS_UPDATE,
);

/**
 * Debounced version of updateStats for window focus events
 * Prevents excessive API calls during rapid window focus/unfocus cycles
 */
export const debouncedUpdateStatsForWindowFocus = debounce(
  updateStats,
  TIME.DEBOUNCE_DELAYS.WINDOW_FOCUS,
);

/**
 * Creates a debounced version of any async function with a custom delay
 * @param func The async function to debounce
 * @param delay The debounce delay in milliseconds
 * @returns A debounced version of the function
 */
export function createDebouncedFunction<T extends (...args: any[]) => Promise<any>>(
  func: T,
  delay: number,
): (...args: Parameters<T>) => void {
  return debounce(func, delay);
}

/**
 * Utility to cancel all pending debounced operations
 * Useful for cleanup during extension deactivation
 */
const activeDebouncedOperations = new Set<NodeJS.Timeout>();

/**
 * Enhanced debounce function that tracks active operations for cleanup
 * @param func The async function to debounce
 * @param delay The debounce delay in milliseconds
 * @returns A debounced version of the function with cleanup tracking
 */
export function debounceWithCleanup<T extends (...args: any[]) => Promise<any>>(
  func: T,
  delay: number,
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout | null = null;

  return (...args: Parameters<T>) => {
    // Clear the previous timeout if it exists
    if (timeoutId) {
      clearTimeout(timeoutId);
      activeDebouncedOperations.delete(timeoutId);
    }

    // Set a new timeout
    timeoutId = setTimeout(async () => {
      try {
        await func(...args);
      } catch (error) {
        // Error handling is done within the original function
      } finally {
        if (timeoutId) {
          activeDebouncedOperations.delete(timeoutId);
        }
        timeoutId = null;
      }
    }, delay);

    // Track the active operation
    activeDebouncedOperations.add(timeoutId);
  };
}

/**
 * Cancels all pending debounced operations
 * Should be called during extension deactivation
 */
export function cancelAllDebouncedOperations(): void {
  activeDebouncedOperations.forEach((timeoutId) => {
    clearTimeout(timeoutId);
  });
  activeDebouncedOperations.clear();
}
