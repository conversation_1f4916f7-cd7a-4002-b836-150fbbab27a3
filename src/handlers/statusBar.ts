import * as vscode from 'vscode';
import { IStatusBarService } from '../services/interfaces';
import { getServices } from '../services/registry';
import { convertAndFormatCurrency } from '../utils/currency';
import { t } from '../utils/i18n';
import { getExtensionContext } from '../extension';
import {
  shouldShowProgressBars,
  createPeriodProgressBar,
  createUsageProgressBar,
  calculateDailyRemaining,
  getMonthNumber,
} from '../utils/progressBars';
import { UI, CONFIG_DEFAULTS, VALIDATION, MONTHS } from '../constants';
import { SafeAccess } from '../utils/validation';

let statusBarItem: vscode.StatusBarItem;

// Define the structure for custom color thresholds
interface ColorThreshold {
  percentage: number;
  color: string; // Can be a theme color ID or a hex code
}

export function createStatusBarItem(): vscode.StatusBarItem {
  const services = getServices();
  services.loggingService.log('[Status Bar] Creating status bar item...');
  statusBarItem = vscode.window.createStatusBarItem(
    vscode.StatusBarAlignment.Right,
    UI.STATUS_BAR_PRIORITY,
  );
  services.loggingService.log(
    `[Status Bar] Status bar alignment: Right, Priority: ${UI.STATUS_BAR_PRIORITY}`,
  );
  return statusBarItem;
}

export function formatTooltipLine(text: string, maxWidth: number = UI.TOOLTIP_MAX_WIDTH): string {
  if (text.length <= maxWidth) {
    return text;
  }
  const words = text.split(' ');
  let lines = [];
  let currentLine = '';

  for (const word of words) {
    if ((currentLine + word).length > maxWidth) {
      if (currentLine) {
        lines.push(currentLine.trim());
      }
      currentLine = word;
    } else {
      currentLine += (currentLine ? ' ' : '') + word;
    }
  }
  if (currentLine) {
    lines.push(currentLine.trim());
  }
  return lines.join('\n   ');
}

export function getMaxLineWidth(lines: string[]): number {
  return Math.max(...lines.map((line) => line.length));
}

export function createSeparator(width: number): string {
  const separatorWidth = Math.floor(width / UI.SEPARATOR_WIDTH_DIVISOR);
  return '╌'.repeat(separatorWidth + UI.SEPARATOR_PADDING);
}

export function formatRelativeTime(dateString: string): string {
  const date = new Date(dateString);
  const hours = date.getHours().toString().padStart(UI.TIME_PADDING, UI.TIME_PADDING_CHAR);
  const minutes = date.getMinutes().toString().padStart(UI.TIME_PADDING, UI.TIME_PADDING_CHAR);
  const seconds = date.getSeconds().toString().padStart(UI.TIME_PADDING, UI.TIME_PADDING_CHAR);

  return `${hours}:${minutes}:${seconds}`;
}

export async function createMarkdownTooltip(
  lines: string[],
  isError: boolean = false,
  allLines: string[] = [],
): Promise<vscode.MarkdownString> {
  const tooltip = new vscode.MarkdownString();
  tooltip.isTrusted = true;
  tooltip.supportHtml = true;
  tooltip.supportThemeIcons = true;

  // Header section with centered title
  tooltip.appendMarkdown('<div align="center">\n\n');
  tooltip.appendMarkdown(`## ⚡ ${t('statusBar.cursorUsageStats')}\n\n`);
  tooltip.appendMarkdown('</div>\n\n');

  if (isError) {
    tooltip.appendMarkdown(`> ⚠️ **${t('statusBar.errorState')}**\n\n`);
    tooltip.appendMarkdown(lines.join('\n\n'));
  } else {
    // Premium Requests Section
    // Check for the translated premium fast requests section
    const premiumRequestsSection = lines.find(
      (line) => line === t('statusBar.premiumFastRequests'),
    );
    if (premiumRequestsSection) {
      tooltip.appendMarkdown('<div align="center">\n\n');
      tooltip.appendMarkdown(`### 🚀 ${t('statusBar.premiumFastRequests')}\n\n`);
      tooltip.appendMarkdown('</div>\n\n');

      // Extract and format premium request info
      const requestLine = lines.find((line) => line.includes(t('statusBar.requestsUsed')));
      const percentLine = lines.find((line) => line.includes(t('statusBar.utilized')));
      const startOfMonthLine = lines.find((line) =>
        line.includes(t('statusBar.fastRequestsPeriod')),
      );

      if (requestLine) {
        // Extract usage information from request line and percentage line
        const usageMatch = requestLine.match(/(\d+)\/(\d+)/);
        const percentMatch = percentLine ? percentLine.match(/(\d+)%/) : null;

        if (usageMatch && usageMatch.length >= 3 && percentMatch && percentMatch.length >= 2) {
          const used = parseInt(usageMatch[1] || '0');
          const total = parseInt(usageMatch[2] || '0');
          const percent = parseInt(percentMatch[1] || '0');

          let displayText = `${used}/${total} (${percent}%) ${t('statusBar.used')}`;

          if (startOfMonthLine) {
            const periodInfo = startOfMonthLine.split(':')[1]?.trim();
            if (periodInfo) {
              displayText = `${periodInfo} ● ${displayText}`;

              // Calculate date elapsed percentage
              const [startDate, endDate] = periodInfo.split('-').map((d) => d.trim());
              if (startDate && endDate) {
                const elapsedPercent = Math.round(
                  calculateDateElapsedPercentage(startDate, endDate),
                );
                displayText = `${periodInfo} (${elapsedPercent}%) ● ${used}/${total} (${percent}%) ${t('statusBar.used')}`;
              }
            }

            // Display the text
            tooltip.appendMarkdown(`<div align="center">${displayText}</div>\n\n`);

            // Add progress bar for premium requests
            if (shouldShowProgressBars() && periodInfo) {
              // First add usage progress bar
              const usageProgressBar = createUsageProgressBar(used, total, t('statusBar.usage'));
              if (usageProgressBar) {
                tooltip.appendMarkdown(`<div align="center">${usageProgressBar}</div>\n\n`);
              }

              // Then add period progress bar
              const periodProgressBar = createPeriodProgressBar(
                periodInfo,
                undefined,
                t('statusBar.period'),
              );
              if (periodProgressBar) {
                tooltip.appendMarkdown(`<div align="center">${periodProgressBar}</div>\n\n`);
              }
            }

            // Add weekday indication and daily remaining calculation (independent of progress bars)
            if (periodInfo) {
              // Parse the end date from the period info
              const [startDateStr, endDateStr] = periodInfo.split('-').map((d) => d.trim());
              const currentYear = new Date().getFullYear();

              if (endDateStr && startDateStr) {
                const endParts = endDateStr.split(' ');
                const endDay = SafeAccess.parseInt(
                  SafeAccess.arrayElementOrUndefined(endParts, 0),
                  1,
                );
                const endMonth = getMonthNumber(SafeAccess.arrayElement(endParts, 1, ''));
                let periodEndDate = new Date(currentYear, endMonth, endDay);

                // If end date is before start date, it means the period crosses into next year
                const startParts = startDateStr.split(' ');
                const startDay = SafeAccess.parseInt(
                  SafeAccess.arrayElementOrUndefined(startParts, 0),
                  1,
                );
                const startMonth = getMonthNumber(SafeAccess.arrayElement(startParts, 1, ''));
                const periodStartDate = new Date(currentYear, startMonth, startDay);

                if (periodEndDate < periodStartDate) {
                  periodEndDate.setFullYear(currentYear + 1);
                }

                const dailyRemainingText = calculateDailyRemaining(used, total, periodEndDate);
                if (dailyRemainingText) {
                  // Handle multi-line daily remaining text
                  const lines = dailyRemainingText.split('\n');
                  lines.forEach((line) => {
                    if (line.trim()) {
                      tooltip.appendMarkdown(`<div align="center">${line.trim()}</div>\n\n`);
                    }
                  });
                }
              }
            }
          } else {
            tooltip.appendMarkdown(`<div align="center">${displayText}</div>\n\n`);
          }
        } else {
          // Fallback to original format if parsing fails
          let displayText = `${SafeAccess.splitAndGet(requestLine, '•', 1)}`;

          if (startOfMonthLine) {
            const periodInfo = SafeAccess.splitAndGet(startOfMonthLine, ':', 1);
            if (periodInfo) {
              displayText = `${periodInfo} ● ${displayText}`;
            }
          }

          tooltip.appendMarkdown(`<div align="center">${displayText}</div>\n\n`);
        }
      }
    }

    // Usage Based Pricing Section
    const services = getServices();
    const token = await services.databaseService.getCursorTokenFromDB();
    let isEnabled = false;

    if (token) {
      try {
        // Check if user is a team member to pass teamId to the usage-based status check
        const context = getExtensionContext();
        const teamInfo = await services.teamService.checkTeamMembership(token, context);

        // Use the new checkUsageBasedStatus function with teamId if available
        const usageStatus = await services.apiService.checkUsageBasedStatus(token, teamInfo.teamId);
        isEnabled = usageStatus.isEnabled;
        const limitResponse = await services.apiService.getCurrentUsageLimit(
          token,
          teamInfo.teamId,
        );

        // Find the original USD data from allLines
        let originalUsageData = null;
        if (allLines && allLines.length > 0) {
          const metadataLine = allLines.find((line) => line.includes('__USD_USAGE_DATA__:'));
          if (metadataLine) {
            try {
              const jsonStr = SafeAccess.splitAndGet(metadataLine, '__USD_USAGE_DATA__:', 1);
              originalUsageData = JSON.parse(jsonStr);
            } catch (e: any) {
              services.loggingService.log(
                '[Status Bar] Error parsing USD data: ' + e.message,
                true,
              );
            }
          }
        }

        const costLine = lines.find((line) => line.includes(t('statusBar.totalCost')));
        let totalCost = 0;
        let formattedTotalCost = '';

        if (costLine) {
          // Extract the cost value, regardless of currency format
          const costMatch = costLine.match(/[^0-9]*([0-9.,]+)/);
          if (costMatch && costMatch[1]) {
            // Convert back to a number, removing any non-numeric characters except decimal point
            totalCost = parseFloat(costMatch[1].replace(/[^0-9.]/g, ''));
            formattedTotalCost = SafeAccess.splitAndGet(costLine, ':', 1);
          }
        }

        const usageBasedPeriodLine = lines.find((line) =>
          line.includes(t('statusBar.usageBasedPeriod')),
        );

        tooltip.appendMarkdown('<div align="center">\n\n');
        tooltip.appendMarkdown(
          `### 📈 ${t('statusBar.usageBasedPricing')} (${isEnabled ? t('statusBar.enabled') : t('statusBar.disabled')})\n\n`,
        );
        tooltip.appendMarkdown('</div>\n\n');

        if (isEnabled && limitResponse.hardLimit) {
          if (usageBasedPeriodLine) {
            const periodText = SafeAccess.splitAndGet(usageBasedPeriodLine, ':', 1);

            // Use the original USD data for percentage calculation if available
            let usagePercentage = '0.0';
            if (originalUsageData && originalUsageData.percentage) {
              usagePercentage = originalUsageData.percentage;
            } else {
              // Fallback to calculating with converted values
              usagePercentage = ((totalCost / limitResponse.hardLimit) * 100).toFixed(1);
            }

            // Convert the limit to the user's preferred currency
            const formattedLimit = await convertAndFormatCurrency(limitResponse.hardLimit);

            // Calculate date elapsed percentage for usage-based period
            const [startDate, endDate] = periodText.split('-').map((d) => d.trim());
            if (startDate && endDate) {
              const elapsedPercent = Math.round(calculateDateElapsedPercentage(startDate, endDate));

              tooltip.appendMarkdown(
                `<div align="center">${periodText} (${elapsedPercent}%) ● ${formattedLimit} (${usagePercentage}% | ${formattedTotalCost} ${t('statusBar.used')})</div>\n\n`,
              );

              // Add usage-based pricing progress bar
              if (shouldShowProgressBars()) {
                const usageProgressBar = createUsageProgressBar(
                  parseFloat(usagePercentage),
                  100,
                  t('statusBar.usage'),
                );
                if (usageProgressBar) {
                  tooltip.appendMarkdown(`<div align="center">${usageProgressBar}</div>\n\n`);
                }

                // Add period progress bar
                const periodProgressBar = createPeriodProgressBar(
                  periodText,
                  undefined,
                  t('statusBar.period'),
                );
                if (periodProgressBar) {
                  tooltip.appendMarkdown(`<div align="center">${periodProgressBar}</div>\n\n`);
                }
              }
            }
          }
        } else if (!isEnabled) {
          tooltip.appendMarkdown(`> ℹ️ ${t('statusBar.usageBasedDisabled')}\n\n`);
        }

        // Show usage details regardless of enabled/disabled status
        // Filter out the mid-month payment item before displaying
        const pricingLines = lines
          .filter(
            (line) =>
              (line.includes('*') || line.includes('→')) &&
              line.includes('➜') &&
              !line.includes(t('api.midMonthPayment')), // Exclude the mid-month payment line item
          )
          .sort((a, b) => {
            // Extract request count from the line (e.g., "   • **042** req @ $0.001~ ➜  **$0.04**   (gpt-4-turbo)")
            // The count is between the first pair of double asterisks.
            const countA = parseInt(a.match(/\*\*(\d+)\*\*/)?.[1] || '0');
            const countB = parseInt(b.match(/\*\*(\d+)\*\*/)?.[1] || '0');
            return countB - countA; // Sort in descending order
          });

        if (pricingLines.length > 0) {
          // Find mid-month payment from the lines directly
          const informationalMidMonthLine = lines.find((line) =>
            line.includes(
              SafeAccess.arrayElement(t('statusBar.youHavePaid').split(' {amount}'), 0, ''),
            ),
          );
          let midMonthPayment = 0;
          let formattedMidMonthPayment = '';

          if (informationalMidMonthLine) {
            // Extract the payment amount from the informational line
            const paymentMatch = informationalMidMonthLine.match(/paid ([^ ]+)/); // Match the amount after "paid "
            if (paymentMatch && paymentMatch[1]) {
              formattedMidMonthPayment = paymentMatch[1];
              // Attempt to parse the numerical value, removing currency symbols/commas
              midMonthPayment = parseFloat(formattedMidMonthPayment.replace(/[^0-9.]/g, '')) || 0;
            }
          }

          const unpaidAmount = totalCost - midMonthPayment;

          // Use formatted output directly
          pricingLines.forEach((line) => {
            tooltip.appendMarkdown(`• ${line.replace('•', '').trim()}\n\n`);
          });

          // Add mid-month payment message if it exists (using the found informational line)
          if (informationalMidMonthLine) {
            const unpaidPrefix = SafeAccess.arrayElement(
              t('statusBar.unpaidAmount').split(' {amount}'),
              0,
              '',
            );
            const unpaidLine = lines.find((line) => line.includes(unpaidPrefix));
            let extractedUnpaidAmountStr = unpaidLine
              ? SafeAccess.splitAndGet(unpaidLine, unpaidPrefix + ':', 1)
              : undefined;
            if (extractedUnpaidAmountStr && extractedUnpaidAmountStr.endsWith(')')) {
              extractedUnpaidAmountStr = extractedUnpaidAmountStr.slice(0, -1);
            }
            const formattedUnpaidAmount =
              extractedUnpaidAmountStr || (await convertAndFormatCurrency(unpaidAmount));

            // Use the already formatted informational line, just add the unpaid part dynamically
            tooltip.appendMarkdown(
              `> ${informationalMidMonthLine.trim()}. (${t('statusBar.unpaidAmount', { amount: `**${formattedUnpaidAmount}**` })})\n\n`,
            );
          }
        } else {
          tooltip.appendMarkdown(`> ℹ️ ${t('statusBar.noUsageRecorded')}\n\n`);
        }
      } catch (error: any) {
        services.loggingService.log(
          '[API] Error fetching limit for tooltip: ' + error.message,
          true,
        );
        tooltip.appendMarkdown(`> ⚠️ ${t('statusBar.errorCheckingStatus')}\n\n`);
      }
    } else {
      tooltip.appendMarkdown(`> ⚠️ ${t('statusBar.unableToCheckStatus')}\n\n`);
    }
  }

  // Action Buttons Section with new compact design
  tooltip.appendMarkdown('---\n\n');
  tooltip.appendMarkdown('<div align="center">\n\n');

  // First row: Account and Extension settings
  tooltip.appendMarkdown(
    `🌐 [${t('statusBar.accountSettings')}](https://www.cursor.com/settings) • `,
  );
  tooltip.appendMarkdown(`🌍 [${t('statusBar.currency')}](command:cursor-stats.selectCurrency) • `);
  tooltip.appendMarkdown(
    `⚙️ [${t('statusBar.extensionSettings')}](command:workbench.action.openSettings?%22@ext%3ADwtexe.cursor-stats%22)\n\n`,
  );

  // Second row: Usage Based Pricing, Refresh, and Last Updated
  const updatedLine = lines.find((line) => line.includes(t('time.lastUpdated')));
  const updatedTime = updatedLine
    ? formatRelativeTime(updatedLine.split(':').slice(1).join(':').trim())
    : new Date().toLocaleTimeString();

  tooltip.appendMarkdown(
    `💰 [${t('statusBar.usageBasedPricing')}](command:cursor-stats.setLimit) • `,
  );
  tooltip.appendMarkdown(`🔄 [${t('statusBar.refresh')}](command:cursor-stats.refreshStats) • `);
  tooltip.appendMarkdown(`🕒 ${updatedTime}\n\n`);

  tooltip.appendMarkdown('</div>');

  return tooltip;
}

export function getStatusBarColor(percentage: number): vscode.ThemeColor | string {
  const config = vscode.workspace.getConfiguration('cursorStats');
  const colorsEnabled = config.get<boolean>('enableStatusBarColors', true);
  const customThresholds = config.get<ColorThreshold[]>('statusBarColorThresholds');

  const defaultColor: vscode.ThemeColor | string = new vscode.ThemeColor(
    'statusBarItem.foreground',
  ); // Default color if disabled or no match

  if (!colorsEnabled) {
    return defaultColor;
  }

  if (customThresholds && customThresholds.length > 0) {
    // Sort thresholds in descending order of percentage
    const sortedThresholds = [...customThresholds].sort((a, b) => b.percentage - a.percentage);

    // Find the first threshold that the percentage meets or exceeds
    const matchedThreshold = sortedThresholds.find(
      (threshold) => percentage >= threshold.percentage,
    );

    if (matchedThreshold) {
      // Check if the color is a hex code or a theme color ID
      if (matchedThreshold.color.startsWith('#')) {
        return matchedThreshold.color; // Return hex string directly
      } else {
        return new vscode.ThemeColor(matchedThreshold.color); // Return ThemeColor instance
      }
    }
  }

  // Fallback to original hardcoded logic if no custom thresholds or no match found
  if (percentage >= CONFIG_DEFAULTS.USAGE_THRESHOLDS.CRITICAL_95) {
    return CONFIG_DEFAULTS.USAGE_COLORS.CRITICAL_95;
  } else if (percentage >= CONFIG_DEFAULTS.USAGE_THRESHOLDS.CRITICAL_90) {
    return CONFIG_DEFAULTS.USAGE_COLORS.CRITICAL_90;
  } else if (percentage >= CONFIG_DEFAULTS.USAGE_THRESHOLDS.HIGH_85) {
    return CONFIG_DEFAULTS.USAGE_COLORS.HIGH_85;
  } else if (percentage >= CONFIG_DEFAULTS.USAGE_THRESHOLDS.HIGH_80) {
    return CONFIG_DEFAULTS.USAGE_COLORS.HIGH_80;
  } else if (percentage >= CONFIG_DEFAULTS.USAGE_THRESHOLDS.MEDIUM_HIGH_75) {
    return CONFIG_DEFAULTS.USAGE_COLORS.MEDIUM_HIGH_75;
  } else if (percentage >= CONFIG_DEFAULTS.USAGE_THRESHOLDS.MEDIUM_HIGH_70) {
    return CONFIG_DEFAULTS.USAGE_COLORS.MEDIUM_HIGH_70;
  } else if (percentage >= CONFIG_DEFAULTS.USAGE_THRESHOLDS.MEDIUM_65) {
    return CONFIG_DEFAULTS.USAGE_COLORS.MEDIUM_65;
  } else if (percentage >= CONFIG_DEFAULTS.USAGE_THRESHOLDS.MEDIUM_60) {
    return CONFIG_DEFAULTS.USAGE_COLORS.MEDIUM_60;
  } else if (percentage >= CONFIG_DEFAULTS.USAGE_THRESHOLDS.MEDIUM_LOW_50) {
    return CONFIG_DEFAULTS.USAGE_COLORS.MEDIUM_LOW_50;
  } else if (percentage >= CONFIG_DEFAULTS.USAGE_THRESHOLDS.LOW_40) {
    return CONFIG_DEFAULTS.USAGE_COLORS.LOW_40;
  } else if (percentage >= CONFIG_DEFAULTS.USAGE_THRESHOLDS.LOW_30) {
    return CONFIG_DEFAULTS.USAGE_COLORS.LOW_30;
  } else if (percentage >= CONFIG_DEFAULTS.USAGE_THRESHOLDS.VERY_LOW_20) {
    return CONFIG_DEFAULTS.USAGE_COLORS.VERY_LOW_20;
  } else if (percentage >= CONFIG_DEFAULTS.USAGE_THRESHOLDS.MINIMAL_10) {
    return CONFIG_DEFAULTS.USAGE_COLORS.MINIMAL_10;
  } else {
    // If percentage is below all custom/default thresholds, use the default color
    return '#FFFFFF';
  }
}

export function getMonthName(month: number): string {
  const monthKey = MONTHS.KEYS[month - VALIDATION.FIRST_MONTH];
  return monthKey ? t(`statusBar.months.${monthKey}`) : `${t('statusBar.month')} ${month}`;
}

function calculateDateElapsedPercentage(startDateStr: string, endDateStr: string): number {
  // Parse dates in "DD Month" format
  const parseDate = (dateStr: string) => {
    const [day, month] = dateStr.trim().split(' ');

    // Build translation map for current language
    const months: { [key: string]: number } = {};

    // English month names (fallback)
    const englishMonths = MONTHS.NAME_TO_NUMBER;

    // Add English names
    Object.assign(months, englishMonths);

    // Add translated names
    for (let i = 0; i < VALIDATION.MONTHS_IN_YEAR; i++) {
      const translatedName = t(`statusBar.months.${MONTHS.KEYS[i]}`);
      months[translatedName] = i;
    }

    const currentYear = new Date().getFullYear();
    const monthIndex = month ? months[month] : undefined;

    if (monthIndex === undefined) {
      const services = getServices();
      services.loggingService.log(`[StatusBar] Could not parse month: ${month}`, true);
      return new Date(); // Return current date as fallback
    }

    return new Date(currentYear, monthIndex, SafeAccess.parseInt(day, 1));
  };

  const startDate = parseDate(startDateStr);
  const endDate = parseDate(endDateStr);
  const currentDate = new Date();

  // Adjust year if the end date is in the next year
  if (endDate < startDate) {
    endDate.setFullYear(endDate.getFullYear() + 1);
  }

  // If current date is before start date, return 0%
  if (currentDate < startDate) {
    return 0;
  }

  // If current date is after end date, return 100%
  if (currentDate > endDate) {
    return 100;
  }

  const totalDuration = endDate.getTime() - startDate.getTime();
  const elapsedDuration = currentDate.getTime() - startDate.getTime();

  return Math.min(Math.max((elapsedDuration / totalDuration) * 100, 0), 100);
}

/**
 * Implementation of the status bar service interface
 * Handles status bar creation, updates, and tooltip management
 */
export class StatusBarService implements IStatusBarService {
  createStatusBarItem(): vscode.StatusBarItem {
    return createStatusBarItem();
  }

  async createMarkdownTooltip(
    lines: string[],
    isError?: boolean,
    allLines?: string[],
  ): Promise<vscode.MarkdownString> {
    return createMarkdownTooltip(lines, isError, allLines);
  }

  formatTooltipLine(text: string, maxWidth?: number): string {
    return formatTooltipLine(text, maxWidth);
  }

  getStatusBarColor(percentage: number): vscode.ThemeColor | string {
    return getStatusBarColor(percentage);
  }
}

// Create a singleton instance for use throughout the application
export const statusBarService = new StatusBarService();
